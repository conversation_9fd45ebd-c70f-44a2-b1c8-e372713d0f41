<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dynamic Timeline Example</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            color: white;
            margin: 0;
            padding: 40px 20px;
        }

        .experience {
            max-width: 1200px;
            margin: 0 auto;
            text-align: center;
        }

        .experience h2 {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 60px;
            color: #4B0082;
        }

        .timeline {
            position: relative;
            max-width: 1000px;
            margin: 0 auto;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 3px;
            background: linear-gradient(180deg, #4B0082, #FF2D55, #4B0082);
            transform: translateX(-50%);
            z-index: 1;
        }

        .timeline-item {
            position: relative;
            margin: 60px 0;
            opacity: 0;
            animation: fadeInUp 0.8s ease forwards;
        }

        .timeline-item:nth-child(1) { animation-delay: 0.2s; }
        .timeline-item:nth-child(2) { animation-delay: 0.4s; }
        .timeline-item:nth-child(3) { animation-delay: 0.6s; }
        .timeline-item:nth-child(4) { animation-delay: 0.8s; }

        .timeline-item:nth-child(odd) .timeline-content {
            margin-left: 60%;
            text-align: left;
        }

        .timeline-item:nth-child(even) .timeline-content {
            margin-right: 60%;
            text-align: right;
        }

        .timeline-dot {
            position: absolute;
            left: 50%;
            top: 30px;
            width: 20px;
            height: 20px;
            background: linear-gradient(45deg, #4B0082, #FF2D55);
            border-radius: 50%;
            transform: translateX(-50%);
            z-index: 2;
            box-shadow: 0 0 20px rgba(75, 0, 130, 0.5);
        }

        .timeline-content {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 40px 30px;
            position: relative;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .timeline-content:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(75, 0, 130, 0.3);
        }

        .company-logo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: 3px solid #4B0082;
            margin-bottom: 20px;
            object-fit: cover;
        }

        .job-title {
            font-size: 20px;
            font-weight: 700;
            color: #4B0082;
            text-transform: uppercase;
            margin-bottom: 10px;
        }

        .company-name {
            font-size: 18px;
            font-weight: 600;
            color: #FFFFFF;
            margin-bottom: 10px;
        }

        .job-duration {
            font-size: 14px;
            color: #FF2D55;
            text-transform: uppercase;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .job-description {
            font-size: 14px;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 20px;
        }

        .company-link a {
            color: #FF2D55;
            text-decoration: none;
            font-size: 14px;
        }

        .company-link a:hover {
            color: #4B0082;
        }

        .view-details {
            color: #4B0082;
            font-weight: 600;
            font-size: 14px;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .info-box {
            background: rgba(75, 0, 130, 0.2);
            border: 1px solid rgba(75, 0, 130, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin: 40px 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <section class="experience">
        <h2>Professional Experience</h2>
        
        <div class="info-box">
            <p><strong>Dynamic Timeline:</strong> This timeline automatically generates content for each job in the data array.</p>
            <p>Currently showing <span id="job-count">0</span> jobs</p>
        </div>

        <div class="timeline" id="timeline">
            <!-- Timeline items will be generated here by JavaScript -->
        </div>
    </section>

    <script>
        // Sample job data (equivalent to your jobsData.js)
        const jobsData = [
            {
                id: 1,
                slug: "frontend-receeto",
                title: "Frontend Developer Angular",
                company: "Company : Receeto",
                companyLink: "https://receeto.com",
                duration: "2/2025 - 6/2025",
                logo: "/Receeto_logo.jpg",
                logoAlt: "Receeto Logo",
                summary: "A smart and responsive web application built with Angular, designed to enhance shopping and budgeting efficiency through data-driven insights."
            },
            {
                id: 2,
                slug: "3d-ecommerce-platform",
                title: "3D-ecommerce platform UI/UX Designer",
                company: "DigitalStudio Creative",
                companyLink: "https://threed-e-commerce.onrender.com",
                duration: "2022 - 2023",
                logo: "/3D E Logo.png",
                logoAlt: "DigitalStudio Creative Logo",
                summary: "Developed an innovative 3D E-Commerce platform that revolutionizes online shopping through immersive 3D product visualization."
            }
        ];

        // Function to generate timeline HTML
        function generateTimeline() {
            const timeline = document.getElementById('timeline');
            const jobCount = document.getElementById('job-count');
            
            // Update job count
            jobCount.textContent = jobsData.length;
            
            // Generate timeline items
            jobsData.forEach((job, index) => {
                const timelineItem = document.createElement('div');
                timelineItem.className = 'timeline-item';
                timelineItem.innerHTML = `
                    <div class="timeline-dot"></div>
                    <div class="timeline-content" onclick="window.open('/job/${job.slug}', '_blank')">
                        <img src="${job.logo}" alt="${job.logoAlt}" class="company-logo" />
                        <h3 class="job-title">${job.title}</h3>
                        <h4 class="company-name">${job.company}</h4>
                        ${job.companyLink ? `
                            <p class="company-link">
                                <a href="${job.companyLink}" target="_blank" rel="noopener noreferrer" onclick="event.stopPropagation()">
                                    ${job.companyLink}
                                </a>
                            </p>
                        ` : ''}
                        <p class="job-duration">${job.duration}</p>
                        <p class="job-description">${job.summary}</p>
                        <div class="view-details">
                            <span>View Details →</span>
                        </div>
                    </div>
                `;
                timeline.appendChild(timelineItem);
            });
        }

        // Generate timeline when page loads
        document.addEventListener('DOMContentLoaded', generateTimeline);
    </script>
</body>
</html>
